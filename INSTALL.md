# دليل التثبيت - موقع ديار الكرم
# Installation Guide - Diyar Al-Karam Website

## 📋 المتطلبات الأساسية | System Requirements

### الخادم | Server
- **خادم ويب**: Apache 2.4+ أو Nginx 1.18+
- **PHP**: الإصدار 7.4 أو أحدث (يُفضل 8.0+)
- **ذاكرة**: 128 ميجابايت على الأقل
- **مساحة القرص**: 50 ميجابايت على الأقل

### إضافات PHP المطلوبة | Required PHP Extensions
- `session` (مُفعل افتراضياً)
- `json` (مُفعل افتراضياً)
- `mbstring` (للنصوص متعددة البايت)
- `fileinfo` (للتحقق من أنواع الملفات)

## 🚀 خطوات التثبيت | Installation Steps

### 1. تحميل الملفات | Download Files
```bash
# استنساخ المشروع
git clone [repository-url] diyar-alkaram-website
cd diyar-alkaram-website

# أو تحميل ملف ZIP وفك الضغط
unzip diyar-alkaram-website.zip
cd diyar-alkaram-website
```

### 2. رفع الملفات للخادم | Upload to Server
```bash
# نسخ الملفات إلى مجلد الويب
cp -r * /var/www/html/
# أو
rsync -av * user@server:/var/www/html/
```

### 3. تعيين الصلاحيات | Set Permissions
```bash
# صلاحيات الملفات
find . -type f -exec chmod 644 {} \;

# صلاحيات المجلدات
find . -type d -exec chmod 755 {} \;

# صلاحيات خاصة لمجلد logs
chmod 755 logs/
chmod 666 logs/*.log
```

### 4. إعداد الخادم | Server Configuration

#### Apache
تأكد من تفعيل الوحدات التالية:
```apache
LoadModule rewrite_module modules/mod_rewrite.so
LoadModule headers_module modules/mod_headers.so
LoadModule expires_module modules/mod_expires.so
LoadModule deflate_module modules/mod_deflate.so
```

#### Nginx
إضافة التكوين التالي:
```nginx
server {
    listen 80;
    server_name diyar-alkaram.com.iq www.diyar-alkaram.com.iq;
    root /var/www/html;
    index index.php index.html;
    
    # إعادة كتابة URLs
    location / {
        try_files $uri $uri/ @rewrite;
    }
    
    location @rewrite {
        rewrite ^/cars/?$ /cars.php last;
        rewrite ^/about/?$ /index.php last;
        rewrite ^/services/?$ /index.php last;
        rewrite ^/contact/?$ /index.php last;
        rewrite ^/(ar|en)/?$ /index.php?lang=$1 last;
        rewrite ^/(ar|en)/cars/?$ /cars.php?lang=$1 last;
    }
    
    # معالجة ملفات PHP
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # منع الوصول للملفات الحساسة
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(htaccess|htpasswd|ini|log|sh|inc|bak)$ {
        deny all;
    }
}
```

## ⚙️ التكوين | Configuration

### 1. إعداد ملف التكوين | Config File Setup
```php
// تحرير config.php
define('SITE_URL', 'https://your-domain.com');
define('SITE_EMAIL', '<EMAIL>');
define('SITE_PHONE', '+964-XXX-XXX-XXXX');

// إعدادات قاعدة البيانات (للاستخدام المستقبلي)
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 2. إعداد الصور | Images Setup
```bash
# إنشاء مجلدات الصور
mkdir -p assets/images/cars/
mkdir -p assets/images/gallery/

# رفع الصور المطلوبة
# - logo.png (شعار الشركة)
# - hero-car.png (صورة السيارة الرئيسية)
# - hero-bg.jpg (خلفية القسم الرئيسي)
# - about-us.jpg (صورة قسم من نحن)
# - placeholder.jpg (صورة بديلة)
```

### 3. إعداد Google Maps | Google Maps Setup
```php
// في config.php
define('GOOGLE_MAPS_API_KEY', 'your-google-maps-api-key');
define('COMPANY_LATITUDE', '33.3152');  // إحداثيات الشركة
define('COMPANY_LONGITUDE', '44.3661');
```

## 🧪 اختبار التثبيت | Testing Installation

### 1. اختبار محلي | Local Testing
```bash
# تشغيل خادم PHP المحلي
php start-server.php

# أو يدوياً
php -S localhost:8000
```

### 2. اختبار الوظائف | Function Testing
- ✅ تحميل الصفحة الرئيسية
- ✅ تبديل اللغات (العربية/الإنجليزية)
- ✅ عرض صفحة السيارات
- ✅ عمل الفلاتر
- ✅ عرض الخريطة
- ✅ روابط التواصل الاجتماعي

### 3. اختبار الأداء | Performance Testing
```bash
# اختبار سرعة التحميل
curl -w "@curl-format.txt" -o /dev/null -s "http://your-domain.com"

# اختبار ضغط الملفات
curl -H "Accept-Encoding: gzip" -I "http://your-domain.com"
```

## 🔧 استكشاف الأخطاء | Troubleshooting

### مشاكل شائعة | Common Issues

#### 1. خطأ 500 - Internal Server Error
```bash
# فحص سجلات الأخطاء
tail -f /var/log/apache2/error.log
# أو
tail -f logs/php_errors.log

# التحقق من صلاحيات الملفات
ls -la config.php
chmod 644 config.php
```

#### 2. عدم عمل إعادة كتابة URLs
```apache
# تأكد من تفعيل mod_rewrite
a2enmod rewrite
systemctl restart apache2

# التحقق من ملف .htaccess
cat .htaccess
```

#### 3. مشاكل الترميز العربي
```php
// إضافة في بداية كل ملف PHP
header('Content-Type: text/html; charset=UTF-8');

// في قاعدة البيانات
ALTER DATABASE your_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 4. عدم ظهور الصور
```bash
# التحقق من مسارات الصور
ls -la assets/images/
chmod 644 assets/images/*

# التحقق من ملف .htaccess
# تأكد من عدم منع الوصول للصور
```

## 🔒 الأمان | Security

### 1. إعدادات الأمان الأساسية
```bash
# إخفاء إصدار PHP
echo "expose_php = Off" >> /etc/php/8.0/apache2/php.ini

# تعطيل الوظائف الخطيرة
echo "disable_functions = exec,passthru,shell_exec,system" >> /etc/php/8.0/apache2/php.ini
```

### 2. تحديث كلمات المرور
```php
// تغيير مفاتيح الأمان في config.php
define('CSRF_TOKEN_NAME', 'your-unique-token-name');
```

### 3. إعداد HTTPS
```bash
# تثبيت شهادة SSL
certbot --apache -d diyar-alkaram.com.iq -d www.diyar-alkaram.com.iq
```

## 📊 المراقبة | Monitoring

### 1. مراقبة السجلات
```bash
# مراقبة سجلات الوصول
tail -f /var/log/apache2/access.log

# مراقبة سجلات الأخطاء
tail -f logs/error.log
```

### 2. مراقبة الأداء
```bash
# استخدام الذاكرة
ps aux | grep php

# استخدام القرص
df -h
du -sh /var/www/html
```

## 🔄 التحديثات | Updates

### 1. تحديث الملفات
```bash
# نسخ احتياطية
cp -r /var/www/html /backup/website-$(date +%Y%m%d)

# تحديث الملفات
git pull origin main
# أو رفع الملفات الجديدة يدوياً
```

### 2. تحديث قاعدة البيانات (مستقبلياً)
```sql
-- تشغيل سكريبت التحديث
SOURCE update.sql;
```

## 📞 الدعم الفني | Technical Support

في حالة مواجهة مشاكل:

1. **فحص السجلات**: راجع ملفات السجلات في مجلد `logs/`
2. **التحقق من المتطلبات**: تأكد من توفر جميع المتطلبات
3. **اختبار محلي**: جرب تشغيل الموقع محلياً
4. **التواصل**: راسل فريق التطوير مع تفاصيل المشكلة

---

**تم إعداد هذا الدليل بواسطة فريق تطوير موقع ديار الكرم**
**This guide was prepared by Diyar Al-Karam website development team**

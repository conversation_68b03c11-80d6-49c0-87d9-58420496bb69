<?php

/**
 * الصفحة الرئيسية لموقع ديار الكرم للنقل العام والتجارة العامة للسيارات
 * Main page for Diyar Al-Karam Public Transport and General Car Trading
 */

// تحميل ملف التكوين
require_once 'config.php';
?>

<!DOCTYPE html>
<html lang="<?php echo $current_lang; ?>" dir="<?php echo $text_direction; ?>">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang['site_title']; ?></title>

    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>

<body>
    <!-- Header -->
    <?php include 'includes/header.php'; ?>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-background">
            <div class="container">
                <div class="row align-items-center min-vh-100">
                    <div class="col-lg-6" data-aos="fade-right">
                        <h1 class="hero-title"><?php echo $lang['hero_title']; ?></h1>
                        <p class="hero-subtitle"><?php echo $lang['hero_subtitle']; ?></p>
                        <div class="hero-buttons">
                            <a href="cars.php" class="btn btn-primary btn-lg me-3"><?php echo $lang['view_cars']; ?></a>
                            <a href="#about" class="btn btn-outline-light btn-lg"><?php echo $lang['learn_more']; ?></a>
                        </div>
                    </div>
                    <div class="col-lg-6" data-aos="fade-left">
                        <div class="hero-image">
                            <img src="assets/images/hero-car.png" alt="<?php echo $lang['hero_image_alt']; ?>" class="img-fluid">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5" data-aos="fade-up">
                    <h2 class="section-title"><?php echo $lang['about_title']; ?></h2>
                    <p class="section-subtitle"><?php echo $lang['about_subtitle']; ?></p>
                </div>
            </div>
            <div class="row align-items-center">
                <div class="col-lg-6" data-aos="fade-right">
                    <div class="about-content">
                        <h3><?php echo $lang['about_heading']; ?></h3>
                        <p><?php echo $lang['about_description']; ?></p>
                        <div class="about-features">
                            <div class="feature-item">
                                <i class="fas fa-check-circle"></i>
                                <span><?php echo $lang['feature_1']; ?></span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-check-circle"></i>
                                <span><?php echo $lang['feature_2']; ?></span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-check-circle"></i>
                                <span><?php echo $lang['feature_3']; ?></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6" data-aos="fade-left">
                    <div class="about-image">
                        <img src="assets/images/about-us.jpg" alt="<?php echo $lang['about_image_alt']; ?>" class="img-fluid rounded">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services-section py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5" data-aos="fade-up">
                    <h2 class="section-title"><?php echo $lang['services_title']; ?></h2>
                    <p class="section-subtitle"><?php echo $lang['services_subtitle']; ?></p>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-6 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-bus"></i>
                        </div>
                        <h4><?php echo $lang['service_transport_title']; ?></h4>
                        <p><?php echo $lang['service_transport_desc']; ?></p>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-car"></i>
                        </div>
                        <h4><?php echo $lang['service_cars_title']; ?></h4>
                        <p><?php echo $lang['service_cars_desc']; ?></p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <!-- Bootstrap 5.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- AOS Animation JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>

    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });
    </script>
</body>

</html>
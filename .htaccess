# ملف .htaccess لموقع ديار الكرم
# .htaccess file for Diyar Al-Karam website

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# ===== إعدادات الأمان =====

# منع الوصول للملفات الحساسة
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# منع الوصول لملفات التكوين
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول لمجلد logs
RedirectMatch 403 ^/logs/.*$

# منع عرض محتويات المجلدات
Options -Indexes

# حماية من هجمات XSS
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# ===== ضغط الملفات =====
<IfModule mod_deflate.c>
    # ضغط النصوص والملفات
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/ld+json
    AddOutputFilterByType DEFLATE image/svg+xml
    
    # استثناء الملفات المضغوطة مسبقاً
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png|zip|gz|bz2|sit|rar|pdf)$ no-gzip dont-vary
</IfModule>

# ===== تخزين مؤقت للملفات =====
<IfModule mod_expires.c>
    ExpiresActive On
    
    # الصور
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # الخطوط
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # ملفات أخرى
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
</IfModule>

# ===== إعدادات Cache-Control =====
<IfModule mod_headers.c>
    # الصور والملفات الثابتة
    <FilesMatch "\.(ico|pdf|flv|jpg|jpeg|png|gif|webp|js|css|swf|woff|woff2)$">
        Header set Cache-Control "max-age=2592000, public"
    </FilesMatch>
    
    # ملفات HTML و PHP
    <FilesMatch "\.(html|htm|php)$">
        Header set Cache-Control "max-age=0, no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "Wed, 11 Jan 1984 05:00:00 GMT"
    </FilesMatch>
</IfModule>

# ===== إعادة توجيه URLs =====

# إعادة توجيه من www إلى non-www (اختياري)
# RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
# RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# إجبار استخدام HTTPS (اختياري)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# URLs صديقة لمحركات البحث
RewriteRule ^cars/?$ cars.php [L]
RewriteRule ^about/?$ index.php#about [L]
RewriteRule ^services/?$ index.php#services [L]
RewriteRule ^contact/?$ index.php#contact [L]

# تبديل اللغة
RewriteRule ^ar/?$ index.php?lang=ar [L]
RewriteRule ^en/?$ index.php?lang=en [L]
RewriteRule ^ar/cars/?$ cars.php?lang=ar [L]
RewriteRule ^en/cars/?$ cars.php?lang=en [L]

# ===== معالجة الأخطاء =====
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# ===== تحسينات الأداء =====

# تقليل حجم الطلبات
<IfModule mod_setenvif.c>
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png|zip|gz|bz2|sit|rar|pdf)$ no-gzip dont-vary
</IfModule>

# تحسين معالجة الملفات
<IfModule mod_mime.c>
    # تحديد أنواع MIME
    AddType application/javascript .js
    AddType text/css .css
    AddType image/webp .webp
    AddType font/woff .woff
    AddType font/woff2 .woff2
    
    # تفعيل ضغط gzip للملفات النصية
    AddEncoding gzip .gz
    AddType text/plain .txt
    AddType text/html .html .htm
    AddType text/css .css
    AddType application/javascript .js
</IfModule>

# ===== حماية إضافية =====

# منع hotlinking للصور
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?diyar-alkaram\.com [NC]
RewriteCond %{REQUEST_URI} \.(gif|jpe?g|png|webp)$ [NC]
RewriteRule \.(gif|jpe?g|png|webp)$ - [F]

# حماية من SQL injection في URLs
RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER) [NC]
RewriteRule ^(.*)$ - [F,L]

# منع الوصول للملفات المؤقتة
<FilesMatch "\.(tmp|temp|bak|backup|old|orig|save)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# ===== تحسين الخطوط =====
<IfModule mod_headers.c>
    # السماح بتحميل الخطوط من مصادر خارجية
    <FilesMatch "\.(woff|woff2|eot|ttf|otf)$">
        Header set Access-Control-Allow-Origin "*"
    </FilesMatch>
</IfModule>

# ===== إعدادات PHP (إذا كان مسموحاً) =====
<IfModule mod_php7.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value memory_limit 128M
    php_value max_execution_time 30
    php_value session.gc_maxlifetime 3600
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/php_errors.log
</IfModule>

# ===== تحسينات إضافية =====

# تقليل عدد طلبات DNS
<IfModule mod_headers.c>
    Header set Connection keep-alive
</IfModule>

# تحسين معالجة الملفات الكبيرة
LimitRequestBody 10485760

# منع عرض معلومات الخادم
ServerTokens Prod
ServerSignature Off

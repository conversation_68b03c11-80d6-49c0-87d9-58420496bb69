<?php
/**
 * ملف التكوين الرئيسي للموقع
 * Main configuration file
 */

// إعدادات عامة
define('SITE_NAME', 'ديار الكرم للنقل العام والتجارة العامة للسيارات');
define('SITE_NAME_EN', 'Diyar Al-Karam Public Transport & General Car Trading');
define('SITE_URL', 'https://diyar-alkaram.com.iq');
define('SITE_EMAIL', '<EMAIL>');
define('SITE_PHONE', '+964 XXX XXX XXXX');

// إعدادات اللغة
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'en']);

// إعدادات قاعدة البيانات (للاستخدام المستقبلي)
define('DB_HOST', 'localhost');
define('DB_NAME', 'diyar_alkaram');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الأمان
define('SESSION_LIFETIME', 3600); // ساعة واحدة
define('CSRF_TOKEN_NAME', '_token');

// إعدادات الملفات
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5 ميجابايت
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// إعدادات التطبيق
define('CARS_PER_PAGE', 12);
define('ENABLE_CACHE', false);
define('CACHE_LIFETIME', 3600);

// إعدادات التواصل الاجتماعي
define('FACEBOOK_URL', 'https://facebook.com/diyar-alkaram');
define('INSTAGRAM_URL', 'https://instagram.com/diyar-alkaram');
define('WHATSAPP_NUMBER', '+964XXXXXXXXX');
define('TWITTER_URL', 'https://twitter.com/diyar-alkaram');

// إعدادات الخريطة
define('GOOGLE_MAPS_API_KEY', 'YOUR_GOOGLE_MAPS_API_KEY');
define('COMPANY_LATITUDE', '33.3152');
define('COMPANY_LONGITUDE', '44.3661');

// إعدادات البريد الإلكتروني
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_ENCRYPTION', 'tls');

// بدء الجلسة مع إعدادات الأمان
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 1);
    ini_set('session.use_strict_mode', 1);
    session_set_cookie_params([
        'lifetime' => SESSION_LIFETIME,
        'path' => '/',
        'domain' => '',
        'secure' => true,
        'httponly' => true,
        'samesite' => 'Strict'
    ]);
    session_start();
}

// دالة للحصول على اللغة الحالية
function getCurrentLanguage() {
    $lang = isset($_GET['lang']) ? $_GET['lang'] : (isset($_SESSION['lang']) ? $_SESSION['lang'] : DEFAULT_LANGUAGE);
    
    if (!in_array($lang, SUPPORTED_LANGUAGES)) {
        $lang = DEFAULT_LANGUAGE;
    }
    
    $_SESSION['lang'] = $lang;
    return $lang;
}

// دالة لتحميل ملف الترجمة
function loadLanguage($lang = null) {
    if ($lang === null) {
        $lang = getCurrentLanguage();
    }
    
    $lang_file = __DIR__ . "/lang/{$lang}.php";
    if (file_exists($lang_file)) {
        include $lang_file;
        return $lang;
    } else {
        include __DIR__ . "/lang/" . DEFAULT_LANGUAGE . ".php";
        return $lang;
    }
}

// دالة للحصول على اتجاه النص
function getTextDirection($lang = null) {
    if ($lang === null) {
        $lang = getCurrentLanguage();
    }
    return ($lang === 'ar') ? 'rtl' : 'ltr';
}

// دالة لتنظيف المدخلات
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// دالة لتوليد CSRF Token
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

// دالة للتحقق من CSRF Token
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

// دالة لتنسيق الأسعار
function formatPrice($price, $currency = 'USD') {
    return number_format($price) . ' ' . $currency;
}

// دالة لتنسيق التاريخ
function formatDate($date, $lang = null) {
    if ($lang === null) {
        $lang = getCurrentLanguage();
    }
    
    $timestamp = is_string($date) ? strtotime($date) : $date;
    
    if ($lang === 'ar') {
        return date('Y/m/d', $timestamp);
    } else {
        return date('M d, Y', $timestamp);
    }
}

// دالة لضغط HTML
function compressHTML($html) {
    if (!ENABLE_CACHE) {
        return $html;
    }
    
    // إزالة المسافات الزائدة والتعليقات
    $html = preg_replace('/<!--(.|\s)*?-->/', '', $html);
    $html = preg_replace('/\s+/', ' ', $html);
    $html = preg_replace('/>\s+</', '><', $html);
    
    return trim($html);
}

// دالة لتسجيل الأخطاء
function logError($message, $file = null, $line = null) {
    $log_message = date('Y-m-d H:i:s') . " - Error: " . $message;
    if ($file) {
        $log_message .= " in " . $file;
    }
    if ($line) {
        $log_message .= " on line " . $line;
    }
    $log_message .= PHP_EOL;
    
    error_log($log_message, 3, __DIR__ . '/logs/error.log');
}

// إعداد معالج الأخطاء
set_error_handler(function($severity, $message, $file, $line) {
    logError($message, $file, $line);
    return false; // السماح لمعالج الأخطاء الافتراضي بالعمل
});

// إعداد معالج الاستثناءات
set_exception_handler(function($exception) {
    logError($exception->getMessage(), $exception->getFile(), $exception->getLine());
});

// إنشاء مجلد السجلات إذا لم يكن موجوداً
if (!is_dir(__DIR__ . '/logs')) {
    mkdir(__DIR__ . '/logs', 0755, true);
}

// تحديد المنطقة الزمنية
date_default_timezone_set('Asia/Baghdad');

// إعدادات الأمان الإضافية
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

// إعداد Content Security Policy
$csp = "default-src 'self'; ";
$csp .= "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://unpkg.com https://maps.googleapis.com; ";
$csp .= "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com https://unpkg.com; ";
$csp .= "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; ";
$csp .= "img-src 'self' data: https: http:; ";
$csp .= "connect-src 'self' https:; ";
$csp .= "frame-src https://www.google.com;";

header("Content-Security-Policy: " . $csp);

// تحديد ترميز الأحرف
header('Content-Type: text/html; charset=UTF-8');

// بيانات الشركة (يمكن نقلها لقاعدة البيانات لاحقاً)
$company_info = [
    'name_ar' => SITE_NAME,
    'name_en' => SITE_NAME_EN,
    'address_ar' => 'بغداد، العراق',
    'address_en' => 'Baghdad, Iraq',
    'phone' => SITE_PHONE,
    'email' => SITE_EMAIL,
    'working_hours_ar' => 'السبت - الخميس: 8:00 ص - 6:00 م',
    'working_hours_en' => 'Saturday - Thursday: 8:00 AM - 6:00 PM',
    'established_year' => 2010,
    'description_ar' => 'شركة رائدة في مجال النقل العام وتجارة السيارات في العراق',
    'description_en' => 'Leading company in public transport and car trading in Iraq'
];

// تحديد المتغيرات العامة
$current_lang = getCurrentLanguage();
$text_direction = getTextDirection($current_lang);
$lang_data = loadLanguage($current_lang);

// تحديد الصفحة الحالية
$current_page = basename($_SERVER['PHP_SELF'], '.php');

// إعدادات SEO الأساسية
$seo_defaults = [
    'title' => ($current_lang === 'ar') ? SITE_NAME : SITE_NAME_EN,
    'description' => ($current_lang === 'ar') ? 
        'شركة ديار الكرم للنقل العام والتجارة العامة للسيارات - خدمات نقل موثوقة وسيارات عالية الجودة' :
        'Diyar Al-Karam Public Transport & General Car Trading - Reliable transport services and high-quality cars',
    'keywords' => ($current_lang === 'ar') ?
        'نقل عام، تجارة السيارات، سيارات للبيع، نقل بغداد، ديار الكرم' :
        'public transport, car trading, cars for sale, Baghdad transport, Diyar Al-Karam',
    'image' => SITE_URL . '/assets/images/logo.png',
    'url' => SITE_URL . $_SERVER['REQUEST_URI']
];

?>

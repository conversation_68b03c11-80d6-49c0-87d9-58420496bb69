/* 
 * ملف الأنماط الرئيسي لموقع ديار الكرم
 * Main CSS file for Diyar Al-Karam website
 */

/* ===== متغيرات CSS ===== */
:root {
  --primary-color: #2c5aa0;
  --secondary-color: #f39c12;
  --accent-color: #e74c3c;
  --dark-color: #2c3e50;
  --light-color: #ecf0f1;
  --text-dark: #2c3e50;
  --text-light: #7f8c8d;
  --white: #ffffff;
  --gradient-primary: linear-gradient(135deg, #2c5aa0 0%, #3498db 100%);
  --gradient-secondary: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 5px 20px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 10px 30px rgba(0, 0, 0, 0.2);
  --border-radius: 8px;
  --transition: all 0.3s ease;
}

/* ===== إعدادات عامة ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Roboto', sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  overflow-x: hidden;
}

/* خطوط مختلفة حسب اللغة */
html[lang='ar'] body {
  font-family: 'Cairo', sans-serif;
}

html[lang='en'] body {
  font-family: 'Roboto', sans-serif;
}

/* ===== تنسيق النصوص ===== */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  margin-bottom: 1rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 1rem;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--gradient-primary);
  border-radius: 2px;
}

html[dir='rtl'] .section-title::after {
  right: 50%;
  transform: translateX(50%);
}

.section-subtitle {
  font-size: 1.1rem;
  color: var(--text-light);
  max-width: 600px;
  margin: 0 auto;
}

/* ===== الهيدر والتنقل ===== */
.main-header {
  position: relative;
  z-index: 1000;
}

.navbar {
  background: rgba(44, 90, 160, 0.95) !important;
  backdrop-filter: blur(10px);
  padding: 1rem 0;
  transition: var(--transition);
}

.navbar.scrolled {
  background: var(--primary-color) !important;
  padding: 0.5rem 0;
  box-shadow: var(--shadow-medium);
}

.navbar-brand {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--white) !important;
  text-decoration: none;
}

.logo-img {
  height: 40px;
  margin-right: 10px;
}

html[dir='rtl'] .logo-img {
  margin-right: 0;
  margin-left: 10px;
}

.navbar-nav .nav-link {
  color: var(--white) !important;
  font-weight: 500;
  margin: 0 0.5rem;
  padding: 0.5rem 1rem !important;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  background: rgba(255, 255, 255, 0.1);
  color: var(--secondary-color) !important;
}

.dropdown-menu {
  background: var(--white);
  border: none;
  box-shadow: var(--shadow-medium);
  border-radius: var(--border-radius);
}

.dropdown-item {
  padding: 0.75rem 1.5rem;
  transition: var(--transition);
}

.dropdown-item:hover,
.dropdown-item.active {
  background: var(--primary-color);
  color: var(--white);
}

/* ===== القسم الرئيسي (Hero) ===== */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: var(--gradient-primary);
  overflow: hidden;
}

.hero-background {
  position: relative;
  z-index: 2;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('../images/hero-bg.jpg') center/cover;
  opacity: 0.1;
  z-index: 1;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 1.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  line-height: 1.8;
}

.hero-buttons .btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  transition: var(--transition);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.btn-primary {
  background: var(--gradient-secondary);
  border: none;
  color: var(--white);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

.btn-outline-light:hover {
  background: var(--white);
  color: var(--primary-color);
  transform: translateY(-2px);
}

.hero-image img {
  max-width: 100%;
  height: auto;
  filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3));
}

/* ===== قسم من نحن ===== */
.about-section {
  padding: 5rem 0;
  background: var(--white);
}

.about-content h3 {
  font-size: 2rem;
  color: var(--dark-color);
  margin-bottom: 1.5rem;
}

.about-content p {
  font-size: 1.1rem;
  color: var(--text-light);
  margin-bottom: 2rem;
  line-height: 1.8;
}

.about-features {
  list-style: none;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.feature-item i {
  color: var(--secondary-color);
  margin-right: 1rem;
  font-size: 1.2rem;
}

html[dir='rtl'] .feature-item i {
  margin-right: 0;
  margin-left: 1rem;
}

.about-image img {
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
}

/* ===== قسم الخدمات ===== */
.services-section {
  padding: 5rem 0;
  background: var(--light-color);
}

.service-card {
  background: var(--white);
  padding: 3rem 2rem;
  border-radius: var(--border-radius);
  text-align: center;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  height: 100%;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-heavy);
}

.service-icon {
  width: 80px;
  height: 80px;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  font-size: 2rem;
  color: var(--white);
}

.service-card h4 {
  font-size: 1.5rem;
  color: var(--dark-color);
  margin-bottom: 1rem;
}

.service-card p {
  color: var(--text-light);
  line-height: 1.8;
}

/* ===== الفوتر ===== */
.main-footer {
  background: var(--dark-color) !important;
}

.contact-section {
  background: var(--dark-color);
}

.footer-widget {
  margin-bottom: 2rem;
}

.widget-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--white);
  margin-bottom: 1.5rem;
  position: relative;
}

.widget-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--secondary-color);
}

html[dir='rtl'] .widget-title::after {
  right: 0;
  left: auto;
}

.widget-text {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.8;
  margin-bottom: 1.5rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  text-decoration: none;
  transition: var(--transition);
}

.social-link:hover {
  background: var(--secondary-color);
  color: var(--white);
  transform: translateY(-2px);
}

.contact-info {
  list-style: none;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  color: rgba(255, 255, 255, 0.8);
}

.contact-item i {
  color: var(--secondary-color);
  margin-right: 1rem;
  width: 20px;
}

html[dir='rtl'] .contact-item i {
  margin-right: 0;
  margin-left: 1rem;
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: var(--transition);
}

.footer-links a:hover {
  color: var(--secondary-color);
}

.footer-links i {
  margin-right: 0.5rem;
  color: var(--secondary-color);
}

html[dir='rtl'] .footer-links i {
  margin-right: 0;
  margin-left: 0.5rem;
}

.bg-darker {
  background: rgba(0, 0, 0, 0.2) !important;
}

.copyright-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.footer-social {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

html[dir='rtl'] .footer-social {
  justify-content: flex-start;
}

/* ===== استجابة الموقع ===== */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .hero-buttons .btn {
    display: block;
    width: 100%;
    margin-bottom: 1rem;
  }

  .navbar-nav {
    text-align: center;
    margin-top: 1rem;
  }

  .footer-social {
    justify-content: center;
    margin-top: 1rem;
  }
}

/* ===== تحسينات إضافية ===== */
.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: var(--transition);
}

.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-medium);
}

/* تأثيرات التحميل */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* تحسين الخطوط للعربية */
html[lang='ar'] {
  font-feature-settings: 'liga' 1, 'kern' 1;
}

/* تحسين المسافات للنصوص العربية */
html[dir='rtl'] .text-end {
  text-align: right !important;
}

html[dir='rtl'] .text-start {
  text-align: left !important;
}

html[dir='rtl'] .me-auto {
  margin-left: auto !important;
  margin-right: 0 !important;
}

html[dir='rtl'] .ms-auto {
  margin-right: auto !important;
  margin-left: 0 !important;
}

/* ===== تحسينات إضافية للأداء ===== */

/* تحسين تحميل الصور */
img {
  max-width: 100%;
  height: auto;
  loading: lazy;
}

/* تحسين الخطوط */
@font-display: swap;

/* تحسين التمرير */
html {
  scroll-behavior: smooth;
}

/* تحسين التركيز */
*:focus {
  outline: 2px solid var(--secondary-color);
  outline-offset: 2px;
}

/* تحسين الطباعة */
@media print {
  .navbar,
  .footer,
  .btn,
  .social-links {
    display: none !important;
  }

  body {
    font-size: 12pt;
    line-height: 1.5;
    color: #000;
    background: #fff;
  }

  .hero-section {
    background: none !important;
    color: #000 !important;
  }
}

/* تحسين الحركة للمستخدمين الذين يفضلون تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* تحسين التباين العالي */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #000080;
    --secondary-color: #ff6600;
    --text-dark: #000000;
    --text-light: #333333;
  }
}

/* تحسين الوضع المظلم */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #4a90e2;
    --dark-color: #1a1a1a;
    --light-color: #2d2d2d;
    --text-dark: #ffffff;
    --text-light: #cccccc;
    --white: #1a1a1a;
  }

  body {
    background-color: var(--dark-color);
    color: var(--text-dark);
  }

  .navbar {
    background: rgba(26, 26, 26, 0.95) !important;
  }

  .card,
  .service-card,
  .car-card {
    background: var(--light-color);
    color: var(--text-dark);
  }
}

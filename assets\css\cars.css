/* 
 * ملف أنماط صفحة السيارات
 * Cars page CSS file
 */

/* ===== رأس الصفحة ===== */
.page-header {
    background: var(--gradient-primary);
    padding: 8rem 0 4rem;
    margin-top: 76px; /* ارتفاع الهيدر */
    color: var(--white);
    text-align: center;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* ===== قسم الفلاتر ===== */
.filters-section {
    border-bottom: 1px solid #e9ecef;
}

.filters-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    align-items: center;
    justify-content: center;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.filter-label {
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
    white-space: nowrap;
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.5rem 1.5rem;
    background: var(--white);
    color: var(--text-dark);
    text-decoration: none;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-weight: 500;
    transition: var(--transition);
    white-space: nowrap;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    text-decoration: none;
}

/* ===== بطاقات السيارات ===== */
.cars-section {
    min-height: 60vh;
}

.car-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    transition: var(--transition);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.car-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.car-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.car-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.car-card:hover .car-image img {
    transform: scale(1.05);
}

.car-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

html[dir="rtl"] .car-badge {
    right: auto;
    left: 1rem;
}

.car-badge.new {
    background: var(--gradient-secondary);
    color: var(--white);
}

.car-badge.used {
    background: rgba(52, 152, 219, 0.9);
    color: var(--white);
}

.car-content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.car-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.car-description {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.car-details {
    margin-bottom: 1.5rem;
    flex: 1;
}

.detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
    color: var(--text-light);
}

.detail-item i {
    color: var(--primary-color);
    margin-right: 0.75rem;
    width: 16px;
    text-align: center;
}

html[dir="rtl"] .detail-item i {
    margin-right: 0;
    margin-left: 0.75rem;
}

.car-footer {
    border-top: 1px solid #f8f9fa;
    padding-top: 1rem;
    margin-top: auto;
}

.car-price {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.price-label {
    font-size: 0.9rem;
    color: var(--text-light);
}

.price-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.car-actions {
    display: flex;
    gap: 0.5rem;
}

.car-actions .btn {
    flex: 1;
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
}

/* ===== رسالة عدم وجود سيارات ===== */
.no-cars-message {
    padding: 4rem 2rem;
    color: var(--text-light);
}

.no-cars-message h3 {
    color: var(--text-light);
    font-weight: 500;
}

/* ===== تحسينات الاستجابة ===== */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .filters-wrapper {
        flex-direction: column;
        gap: 1rem;
    }
    
    .filter-group {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .filter-buttons {
        justify-content: center;
    }
    
    .car-actions {
        flex-direction: column;
    }
    
    .car-actions .btn {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 6rem 0 3rem;
    }
    
    .car-image {
        height: 200px;
    }
    
    .car-content {
        padding: 1rem;
    }
    
    .price-value {
        font-size: 1.3rem;
    }
}

/* ===== تأثيرات إضافية ===== */
.car-card {
    position: relative;
    overflow: hidden;
}

.car-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
    z-index: 1;
}

.car-card:hover::before {
    left: 100%;
}

/* تحسين الخطوط للأرقام */
.price-value,
.detail-item {
    font-feature-settings: "tnum" 1;
}

/* تحسين الألوان للحالة المظلمة */
@media (prefers-color-scheme: dark) {
    .car-card {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .car-title {
        color: #f7fafc;
    }
    
    .car-description,
    .detail-item {
        color: #a0aec0;
    }
    
    .car-footer {
        border-color: #4a5568;
    }
}

/* تحسينات إمكانية الوصول */
.filter-btn:focus,
.car-actions .btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* تأثيرات التحميل */
.car-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.car-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary-color);
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 2;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* تحسين التباعد للنصوص العربية */
html[lang="ar"] .car-title {
    line-height: 1.4;
}

html[lang="ar"] .car-description {
    line-height: 1.8;
}

/* تحسين محاذاة الأزرار للعربية */
html[dir="rtl"] .car-actions {
    direction: ltr;
}

html[dir="rtl"] .car-actions .btn {
    direction: rtl;
}

# موقع ديار الكرم للنقل العام والتجارة العامة للسيارات
# Diyar Al-Karam Public Transport & General Car Trading Website

موقع ويب احترافي وحديث لشركة ديار الكرم للنقل العام والتجارة العامة للسيارات، مبني باستخدام Bootstrap 5.3 و PHP مع دعم تعدد اللغات.

## 🌟 المميزات الرئيسية

### ✅ التقنيات المستخدمة
- **Frontend**: Bootstrap 5.3, HTML5, CSS3, JavaScript ES6
- **Backend**: PHP (بدون إطار عمل)
- **التأثيرات**: AOS.js للتحريك، Animate.css
- **الخطوط**: Google Fonts (Cairo للعربية، Roboto للإنجليزية)
- **الأيقونات**: Font Awesome 6.4

### 🌐 نظام تعدد اللغات
- دعم كامل للغة العربية والإنجليزية
- تبديل سهل بين اللغات
- ملفات ترجمة منظمة (`lang/ar.php`, `lang/en.php`)
- دعم اتجاه النص (RTL/LTR)

### 📱 التصميم المتجاوب
- متوافق مع جميع أحجام الشاشات
- تصميم Mobile-First
- تحسينات خاصة للأجهزة اللوحية والهواتف

## 🏗️ هيكل المشروع

```
project/
├── index.php              # الصفحة الرئيسية
├── cars.php              # صفحة عرض السيارات
├── includes/             # الملفات المشتركة
│   ├── header.php        # الهيدر
│   └── footer.php        # الفوتر
├── lang/                 # ملفات الترجمة
│   ├── ar.php           # الترجمة العربية
│   └── en.php           # الترجمة الإنجليزية
├── assets/              # الملفات الثابتة
│   ├── css/
│   │   ├── style.css    # الأنماط الرئيسية
│   │   └── cars.css     # أنماط صفحة السيارات
│   ├── js/
│   │   └── main.js      # JavaScript الرئيسي
│   └── images/          # الصور
└── README.md            # هذا الملف
```

## 🚀 التثبيت والتشغيل

### المتطلبات
- خادم ويب (Apache/Nginx)
- PHP 7.4 أو أحدث
- دعم لـ PHP Sessions

### خطوات التثبيت

1. **نسخ الملفات**
   ```bash
   git clone [repository-url]
   cd diyar-alkaram-website
   ```

2. **رفع الملفات إلى الخادم**
   - انسخ جميع الملفات إلى مجلد الويب الجذر
   - تأكد من أن PHP يعمل بشكل صحيح

3. **إعداد الصور**
   - أضف الصور المطلوبة في مجلد `assets/images/`
   - راجع ملف `assets/images/placeholder.txt` للتفاصيل

4. **اختبار الموقع**
   - افتح `index.php` في المتصفح
   - تأكد من عمل تبديل اللغات
   - اختبر صفحة السيارات `cars.php`

## 📄 الصفحات المتاحة

### 🏠 الصفحة الرئيسية (`index.php`)
- قسم Hero مع سلايدر ترحيبي
- نبذة عن الشركة
- عرض الخدمات الرئيسية
- معلومات الاتصال

### 🚗 صفحة السيارات (`cars.php`)
- عرض السيارات في بطاقات جذابة
- فلاتر حسب النوع والحالة
- تفاصيل كاملة لكل سيارة
- تأثيرات بصرية متقدمة

## 🎨 التخصيص

### الألوان
يمكن تعديل الألوان من ملف `assets/css/style.css`:
```css
:root {
    --primary-color: #2c5aa0;      /* الأزرق الرئيسي */
    --secondary-color: #f39c12;    /* البرتقالي الثانوي */
    --accent-color: #e74c3c;       /* الأحمر المميز */
    --dark-color: #2c3e50;         /* الرمادي الداكن */
}
```

### إضافة لغة جديدة
1. أنشئ ملف جديد في مجلد `lang/` (مثل `fr.php`)
2. انسخ محتوى `ar.php` وترجم النصوص
3. أضف اللغة إلى مصفوفة `$allowed_languages`
4. أضف خيار اللغة في الهيدر

### إضافة سيارات جديدة
عدّل مصفوفة `$cars` في ملف `cars.php`:
```php
$cars[] = [
    'id' => 5,
    'name_ar' => 'اسم السيارة بالعربية',
    'name_en' => 'Car Name in English',
    'price' => 50000,
    'year' => 2023,
    // ... باقي البيانات
];
```

## 🔧 التحسينات والصيانة

### الأداء
- الصور محسّنة للويب
- تحميل كسول للمحتوى
- ضغط CSS و JavaScript
- استخدام CDN للمكتبات الخارجية

### الأمان
- تنظيف المدخلات
- حماية من XSS
- التحقق من صحة اللغة المختارة
- استخدام HTTPS (موصى به)

### إمكانية الوصول
- دعم لوحة المفاتيح
- نصوص بديلة للصور
- تباين ألوان مناسب
- دعم قارئات الشاشة

## 📞 معلومات الاتصال

**شركة ديار الكرم للنقل العام والتجارة العامة للسيارات**
- 📍 العنوان: بغداد، العراق
- 📞 الهاتف: +964 XXX XXX XXXX
- 📧 البريد: <EMAIL>

## 📝 الترخيص

هذا المشروع مطور خصيصاً لشركة ديار الكرم. جميع الحقوق محفوظة © 2024.

## 🤝 المساهمة

لتحسين الموقع أو إضافة مميزات جديدة:
1. أنشئ Fork للمشروع
2. أنشئ فرع جديد للميزة
3. اختبر التغييرات
4. أرسل Pull Request

## 📋 قائمة المهام المستقبلية

- [ ] إضافة نظام إدارة المحتوى
- [ ] تطوير صفحة تفاصيل السيارة
- [ ] إضافة نظام البحث المتقدم
- [ ] تطوير تطبيق الهاتف المحمول
- [ ] إضافة نظام الحجز الإلكتروني
- [ ] تحسين SEO
- [ ] إضافة المزيد من اللغات

---

**تم التطوير بواسطة فريق تطوير محترف | Developed by Professional Development Team**

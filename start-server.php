<?php
/**
 * ملف تشغيل الخادم المحلي للتطوير
 * Local development server starter
 */

// التحقق من إصدار PHP
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    die("خطأ: يتطلب هذا المشروع PHP 7.4 أو أحدث. الإصدار الحالي: " . PHP_VERSION . "\n");
}

// إعدادات الخادم
$host = 'localhost';
$port = 8000;
$docroot = __DIR__;

// التحقق من توفر المنفذ
$connection = @fsockopen($host, $port);
if (is_resource($connection)) {
    fclose($connection);
    echo "تحذير: المنفذ {$port} مستخدم بالفعل. جاري البحث عن منفذ آخر...\n";
    
    // البحث عن منفذ متاح
    for ($i = 8001; $i <= 8010; $i++) {
        $connection = @fsockopen($host, $i);
        if (!is_resource($connection)) {
            $port = $i;
            break;
        }
        fclose($connection);
    }
}

// إنشاء مجلد logs إذا لم يكن موجوداً
if (!is_dir($docroot . '/logs')) {
    mkdir($docroot . '/logs', 0755, true);
    echo "تم إنشاء مجلد logs\n";
}

// إنشاء ملف .gitignore إذا لم يكن موجوداً
$gitignore_content = "# ملفات النظام\n.DS_Store\nThumbs.db\n\n# ملفات السجلات\nlogs/\n*.log\n\n# ملفات التكوين المحلية\nconfig.local.php\n\n# ملفات النسخ الاحتياطية\n*.bak\n*.backup\n*.tmp\n\n# مجلدات IDE\n.vscode/\n.idea/\n\n# ملفات الرفع\nuploads/\n\n# ملفات التخزين المؤقت\ncache/\n";

if (!file_exists($docroot . '/.gitignore')) {
    file_put_contents($docroot . '/.gitignore', $gitignore_content);
    echo "تم إنشاء ملف .gitignore\n";
}

// رسالة الترحيب
echo "\n";
echo "🚗 ===== موقع ديار الكرم للنقل العام والتجارة العامة للسيارات =====\n";
echo "🌐 Diyar Al-Karam Public Transport & General Car Trading Website\n";
echo "\n";
echo "📂 مجلد المشروع: {$docroot}\n";
echo "🌍 عنوان الخادم: http://{$host}:{$port}\n";
echo "\n";
echo "📋 الصفحات المتاحة:\n";
echo "   • الصفحة الرئيسية: http://{$host}:{$port}/\n";
echo "   • صفحة السيارات: http://{$host}:{$port}/cars.php\n";
echo "   • العربية: http://{$host}:{$port}/?lang=ar\n";
echo "   • English: http://{$host}:{$port}/?lang=en\n";
echo "\n";
echo "🔧 للتوقف: اضغط Ctrl+C\n";
echo "\n";

// بدء الخادم
$command = "php -S {$host}:{$port} -t {$docroot}";

echo "🚀 جاري بدء الخادم...\n";
echo "📝 الأمر: {$command}\n";
echo "\n";

// تسجيل وقت البدء
$start_time = date('Y-m-d H:i:s');
file_put_contents($docroot . '/logs/server.log', "[{$start_time}] Server started on http://{$host}:{$port}\n", FILE_APPEND);

// تشغيل الخادم
passthru($command);

// تسجيل وقت التوقف
$stop_time = date('Y-m-d H:i:s');
file_put_contents($docroot . '/logs/server.log', "[{$stop_time}] Server stopped\n", FILE_APPEND);

echo "\n";
echo "✅ تم إيقاف الخادم بنجاح\n";
echo "📊 يمكنك مراجعة سجلات الخادم في: logs/server.log\n";
?>

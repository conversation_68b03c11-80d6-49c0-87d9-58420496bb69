<?php
/**
 * صفحة خطأ 404 - الصفحة غير موجودة
 * 404 Error Page - Page Not Found
 */

// تحميل ملف التكوين
require_once 'config.php';

// تحديد رمز الاستجابة
http_response_code(404);
?>

<!DOCTYPE html>
<html lang="<?php echo $current_lang; ?>" dir="<?php echo $text_direction; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo ($current_lang == 'ar') ? 'الصفحة غير موجودة - 404' : 'Page Not Found - 404'; ?> | <?php echo $lang['site_title']; ?></title>
    
    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <style>
        .error-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            background: var(--gradient-primary);
            color: var(--white);
        }
        
        .error-content {
            text-align: center;
            padding: 3rem 0;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .error-title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }
        
        .error-description {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .error-actions .btn {
            margin: 0.5rem;
            padding: 1rem 2rem;
            font-weight: 600;
            border-radius: 50px;
        }
        
        .btn-light {
            background: var(--white);
            color: var(--primary-color);
            border: none;
        }
        
        .btn-light:hover {
            background: var(--light-color);
            color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .btn-outline-light:hover {
            background: var(--white);
            color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 5rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .error-description {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="error-page">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="error-content">
                        <div class="error-code">404</div>
                        <h1 class="error-title">
                            <?php echo ($current_lang == 'ar') ? 'الصفحة غير موجودة' : 'Page Not Found'; ?>
                        </h1>
                        <p class="error-description">
                            <?php echo ($current_lang == 'ar') ? 
                                'عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.' : 
                                'Sorry, the page you are looking for does not exist or has been moved to another location.'; ?>
                        </p>
                        
                        <div class="error-actions">
                            <a href="index.php" class="btn btn-light btn-lg">
                                <i class="fas fa-home me-2"></i>
                                <?php echo ($current_lang == 'ar') ? 'العودة للرئيسية' : 'Back to Home'; ?>
                            </a>
                            <a href="cars.php" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-car me-2"></i>
                                <?php echo ($current_lang == 'ar') ? 'تصفح السيارات' : 'Browse Cars'; ?>
                            </a>
                        </div>
                        
                        <div class="mt-4">
                            <p class="mb-2">
                                <?php echo ($current_lang == 'ar') ? 'أو يمكنك التواصل معنا:' : 'Or you can contact us:'; ?>
                            </p>
                            <a href="tel:<?php echo SITE_PHONE; ?>" class="text-white me-3">
                                <i class="fas fa-phone"></i> <?php echo SITE_PHONE; ?>
                            </a>
                            <a href="mailto:<?php echo SITE_EMAIL; ?>" class="text-white">
                                <i class="fas fa-envelope"></i> <?php echo SITE_EMAIL; ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap 5.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إضافة تأثير بسيط للصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const errorContent = document.querySelector('.error-content');
            errorContent.style.opacity = '0';
            errorContent.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                errorContent.style.transition = 'all 0.6s ease';
                errorContent.style.opacity = '1';
                errorContent.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>

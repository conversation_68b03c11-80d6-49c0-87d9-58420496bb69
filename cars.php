<?php

/**
 * صفحة عرض السيارات
 * Cars Display Page
 */

// تحميل ملف التكوين
require_once 'config.php';

// بيانات السيارات التجريبية
$cars = [
    [
        'id' => 1,
        'name_ar' => 'تويوتا كامري 2023',
        'name_en' => 'Toyota Camry 2023',
        'price' => 45000,
        'year' => 2023,
        'mileage' => 0,
        'fuel' => 'بنزين',
        'fuel_en' => 'Gasoline',
        'transmission_ar' => 'أوتوماتيك',
        'transmission_en' => 'Automatic',
        'type' => 'sedan',
        'condition' => 'new',
        'image' => 'assets/images/cars/camry-2023.jpg',
        'description_ar' => 'سيارة تويوتا كامري 2023 جديدة بالكامل مع أحدث المواصفات والتقنيات',
        'description_en' => 'Brand new Toyota Camry 2023 with latest specifications and technologies'
    ],
    [
        'id' => 2,
        'name_ar' => 'هوندا أكورد 2022',
        'name_en' => 'Honda Accord 2022',
        'price' => 38000,
        'year' => 2022,
        'mileage' => 15000,
        'fuel' => 'بنزين',
        'fuel_en' => 'Gasoline',
        'transmission_ar' => 'أوتوماتيك',
        'transmission_en' => 'Automatic',
        'type' => 'sedan',
        'condition' => 'used',
        'image' => 'assets/images/cars/accord-2022.jpg',
        'description_ar' => 'هوندا أكورد 2022 مستعملة بحالة ممتازة وصيانة دورية',
        'description_en' => 'Used Honda Accord 2022 in excellent condition with regular maintenance'
    ],
    [
        'id' => 3,
        'name_ar' => 'نيسان باترول 2023',
        'name_en' => 'Nissan Patrol 2023',
        'price' => 85000,
        'year' => 2023,
        'mileage' => 0,
        'fuel' => 'بنزين',
        'fuel_en' => 'Gasoline',
        'transmission_ar' => 'أوتوماتيك',
        'transmission_en' => 'Automatic',
        'type' => 'suv',
        'condition' => 'new',
        'image' => 'assets/images/cars/patrol-2023.jpg',
        'description_ar' => 'نيسان باترول 2023 جديدة بالكامل مع دفع رباعي وتقنيات متقدمة',
        'description_en' => 'Brand new Nissan Patrol 2023 with 4WD and advanced technologies'
    ],
    [
        'id' => 4,
        'name_ar' => 'تويوتا لاند كروزر 2022',
        'name_en' => 'Toyota Land Cruiser 2022',
        'price' => 95000,
        'year' => 2022,
        'mileage' => 8000,
        'fuel' => 'بنزين',
        'fuel_en' => 'Gasoline',
        'transmission_ar' => 'أوتوماتيك',
        'transmission_en' => 'Automatic',
        'type' => 'suv',
        'condition' => 'used',
        'image' => 'assets/images/cars/landcruiser-2022.jpg',
        'description_ar' => 'تويوتا لاند كروزر 2022 مستعملة بحالة ممتازة ومواصفات عالية',
        'description_en' => 'Used Toyota Land Cruiser 2022 in excellent condition with high specifications'
    ]
];

// فلترة السيارات حسب النوع والحالة
$filter_type = isset($_GET['type']) ? $_GET['type'] : 'all';
$filter_condition = isset($_GET['condition']) ? $_GET['condition'] : 'all';

$filtered_cars = array_filter($cars, function ($car) use ($filter_type, $filter_condition) {
    $type_match = ($filter_type == 'all') || ($car['type'] == $filter_type);
    $condition_match = ($filter_condition == 'all') || ($car['condition'] == $filter_condition);
    return $type_match && $condition_match;
});
?>

<!DOCTYPE html>
<html lang="<?php echo $current_lang; ?>" dir="<?php echo $text_direction; ?>">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $lang['cars_page_title']; ?> - <?php echo $lang['site_title']; ?></title>

    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/cars.css">
</head>

<body>
    <!-- Header -->
    <?php include 'includes/header.php'; ?>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center">
                    <h1 class="page-title" data-aos="fade-up"><?php echo $lang['cars_page_title']; ?></h1>
                    <p class="page-subtitle" data-aos="fade-up" data-aos-delay="100"><?php echo $lang['cars_page_subtitle']; ?></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Filters Section -->
    <section class="filters-section py-4 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="filters-wrapper" data-aos="fade-up">
                        <div class="filter-group">
                            <label class="filter-label"><?php echo ($current_lang == 'ar') ? 'النوع:' : 'Type:'; ?></label>
                            <div class="filter-buttons">
                                <a href="?type=all&condition=<?php echo $filter_condition; ?>"
                                    class="filter-btn <?php echo ($filter_type == 'all') ? 'active' : ''; ?>">
                                    <?php echo $lang['filter_all']; ?>
                                </a>
                                <a href="?type=sedan&condition=<?php echo $filter_condition; ?>"
                                    class="filter-btn <?php echo ($filter_type == 'sedan') ? 'active' : ''; ?>">
                                    <?php echo $lang['filter_sedan']; ?>
                                </a>
                                <a href="?type=suv&condition=<?php echo $filter_condition; ?>"
                                    class="filter-btn <?php echo ($filter_type == 'suv') ? 'active' : ''; ?>">
                                    <?php echo $lang['filter_suv']; ?>
                                </a>
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label"><?php echo ($current_lang == 'ar') ? 'الحالة:' : 'Condition:'; ?></label>
                            <div class="filter-buttons">
                                <a href="?type=<?php echo $filter_type; ?>&condition=all"
                                    class="filter-btn <?php echo ($filter_condition == 'all') ? 'active' : ''; ?>">
                                    <?php echo $lang['filter_all']; ?>
                                </a>
                                <a href="?type=<?php echo $filter_type; ?>&condition=new"
                                    class="filter-btn <?php echo ($filter_condition == 'new') ? 'active' : ''; ?>">
                                    <?php echo $lang['filter_new']; ?>
                                </a>
                                <a href="?type=<?php echo $filter_type; ?>&condition=used"
                                    class="filter-btn <?php echo ($filter_condition == 'used') ? 'active' : ''; ?>">
                                    <?php echo $lang['filter_used']; ?>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Cars Grid -->
    <section class="cars-section py-5">
        <div class="container">
            <div class="row">
                <?php if (empty($filtered_cars)): ?>
                    <div class="col-lg-12 text-center">
                        <div class="no-cars-message" data-aos="fade-up">
                            <i class="fas fa-car fa-3x text-muted mb-3"></i>
                            <h3><?php echo $lang['no_cars_found']; ?></h3>
                        </div>
                    </div>
                <?php else: ?>
                    <?php $delay = 0;
                    foreach ($filtered_cars as $car): ?>
                        <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="<?php echo $delay; ?>">
                            <div class="car-card">
                                <div class="car-image">
                                    <img src="<?php echo $car['image']; ?>" alt="<?php echo ($current_lang == 'ar') ? $car['name_ar'] : $car['name_en']; ?>" class="img-fluid">
                                    <div class="car-badge <?php echo $car['condition']; ?>">
                                        <?php echo ($car['condition'] == 'new') ? $lang['filter_new'] : $lang['filter_used']; ?>
                                    </div>
                                </div>
                                <div class="car-content">
                                    <h4 class="car-title"><?php echo ($current_lang == 'ar') ? $car['name_ar'] : $car['name_en']; ?></h4>
                                    <p class="car-description"><?php echo ($current_lang == 'ar') ? $car['description_ar'] : $car['description_en']; ?></p>

                                    <div class="car-details">
                                        <div class="detail-item">
                                            <i class="fas fa-calendar"></i>
                                            <span><?php echo $lang['car_year']; ?>: <?php echo $car['year']; ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <i class="fas fa-road"></i>
                                            <span><?php echo $lang['car_mileage']; ?>: <?php echo number_format($car['mileage']); ?> كم</span>
                                        </div>
                                        <div class="detail-item">
                                            <i class="fas fa-gas-pump"></i>
                                            <span><?php echo $lang['car_fuel']; ?>: <?php echo ($current_lang == 'ar') ? $car['fuel'] : $car['fuel_en']; ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <i class="fas fa-cogs"></i>
                                            <span><?php echo $lang['car_transmission']; ?>: <?php echo ($current_lang == 'ar') ? $car['transmission_ar'] : $car['transmission_en']; ?></span>
                                        </div>
                                    </div>

                                    <div class="car-footer">
                                        <div class="car-price">
                                            <span class="price-label"><?php echo $lang['car_price']; ?>:</span>
                                            <span class="price-value">$<?php echo number_format($car['price']); ?></span>
                                        </div>
                                        <div class="car-actions">
                                            <a href="#" class="btn btn-outline-primary btn-sm"><?php echo $lang['view_details']; ?></a>
                                            <a href="#" class="btn btn-primary btn-sm"><?php echo $lang['contact_seller']; ?></a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php $delay += 100; ?>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <!-- Bootstrap 5.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- AOS Animation JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>

    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });
    </script>
</body>

</html>
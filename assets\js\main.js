/**
 * ملف JavaScript الرئيسي لموقع ديار الكرم
 * Main JavaScript file for Diyar Al-Karam website
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // ===== تأثيرات الهيدر عند التمرير =====
    const navbar = document.querySelector('.navbar');
    
    function handleScroll() {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }
    
    window.addEventListener('scroll', handleScroll);
    
    // ===== تأثيرات التمرير السلس =====
    const smoothScrollLinks = document.querySelectorAll('a[href^="#"]');
    
    smoothScrollLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                const headerHeight = navbar.offsetHeight;
                const targetPosition = targetElement.offsetTop - headerHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // ===== تأثيرات الخلفية المتحركة للقسم الرئيسي =====
    const heroSection = document.querySelector('.hero-section');
    
    if (heroSection) {
        // إضافة جزيئات متحركة للخلفية
        createAnimatedBackground();
    }
    
    function createAnimatedBackground() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.style.position = 'absolute';
        canvas.style.top = '0';
        canvas.style.left = '0';
        canvas.style.width = '100%';
        canvas.style.height = '100%';
        canvas.style.zIndex = '1';
        canvas.style.pointerEvents = 'none';
        
        heroSection.appendChild(canvas);
        
        function resizeCanvas() {
            canvas.width = heroSection.offsetWidth;
            canvas.height = heroSection.offsetHeight;
        }
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        
        // جزيئات متحركة
        const particles = [];
        const particleCount = 50;
        
        for (let i = 0; i < particleCount; i++) {
            particles.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                size: Math.random() * 3 + 1,
                speedX: (Math.random() - 0.5) * 0.5,
                speedY: (Math.random() - 0.5) * 0.5,
                opacity: Math.random() * 0.5 + 0.2
            });
        }
        
        function animateParticles() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            particles.forEach(particle => {
                particle.x += particle.speedX;
                particle.y += particle.speedY;
                
                // إعادة تدوير الجزيئات
                if (particle.x > canvas.width) particle.x = 0;
                if (particle.x < 0) particle.x = canvas.width;
                if (particle.y > canvas.height) particle.y = 0;
                if (particle.y < 0) particle.y = canvas.height;
                
                // رسم الجزيئة
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`;
                ctx.fill();
            });
            
            requestAnimationFrame(animateParticles);
        }
        
        animateParticles();
    }
    
    // ===== تأثيرات بطاقات السيارات =====
    const carCards = document.querySelectorAll('.car-card');
    
    carCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // ===== تأثيرات الأزرار =====
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // تأثير الموجة عند النقر
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // ===== تحسين الأداء للصور =====
    const images = document.querySelectorAll('img');
    
    // Lazy loading للصور
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                    }
                    img.classList.add('loaded');
                    observer.unobserve(img);
                }
            });
        });
        
        images.forEach(img => {
            imageObserver.observe(img);
        });
    }
    
    // ===== تحسين تجربة المستخدم =====
    
    // إظهار مؤشر التحميل
    function showLoading(element) {
        element.classList.add('loading');
    }
    
    function hideLoading(element) {
        element.classList.remove('loading');
    }
    
    // تحسين النماذج
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitButton = this.querySelector('button[type="submit"]');
            if (submitButton) {
                showLoading(submitButton);
                submitButton.disabled = true;
                
                // إعادة تفعيل الزر بعد 3 ثوان (في حالة عدم إعادة تحميل الصفحة)
                setTimeout(() => {
                    hideLoading(submitButton);
                    submitButton.disabled = false;
                }, 3000);
            }
        });
    });
    
    // ===== تحسين إمكانية الوصول =====
    
    // إضافة دعم لوحة المفاتيح للعناصر التفاعلية
    const interactiveElements = document.querySelectorAll('.car-card, .service-card, .filter-btn');
    
    interactiveElements.forEach(element => {
        element.setAttribute('tabindex', '0');
        
        element.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
    
    // ===== تحسين الأداء =====
    
    // تأخير تحميل المحتوى غير الضروري
    function lazyLoadContent() {
        const lazyElements = document.querySelectorAll('[data-lazy]');
        
        lazyElements.forEach(element => {
            if (element.getBoundingClientRect().top < window.innerHeight + 200) {
                const content = element.dataset.lazy;
                element.innerHTML = content;
                element.removeAttribute('data-lazy');
            }
        });
    }
    
    window.addEventListener('scroll', lazyLoadContent);
    lazyLoadContent(); // تحميل المحتوى المرئي فوراً
    
    // ===== تحسين الاستجابة =====
    
    // تحديث التخطيط عند تغيير حجم الشاشة
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(() => {
            // إعادة حساب المواضع والأحجام
            if (typeof AOS !== 'undefined') {
                AOS.refresh();
            }
        }, 250);
    });
    
    // ===== معالجة الأخطاء =====
    
    // معالجة أخطاء تحميل الصور
    images.forEach(img => {
        img.addEventListener('error', function() {
            this.src = 'assets/images/placeholder.jpg'; // صورة بديلة
            this.alt = 'صورة غير متاحة';
        });
    });
    
    // ===== تحسينات إضافية =====
    
    // إضافة تأثير التمرير للعناصر
    function addScrollEffect() {
        const elements = document.querySelectorAll('.scroll-effect');
        
        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementVisible = 150;
            
            if (elementTop < window.innerHeight - elementVisible) {
                element.classList.add('active');
            }
        });
    }
    
    window.addEventListener('scroll', addScrollEffect);
    
    // تحسين الخطوط العربية
    if (document.documentElement.lang === 'ar') {
        document.body.style.fontFeatureSettings = '"liga" 1, "kern" 1';
    }
    
    console.log('🚗 موقع ديار الكرم تم تحميله بنجاح!');
});

// ===== CSS للتأثيرات =====
const style = document.createElement('style');
style.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .loading {
        position: relative;
        pointer-events: none;
    }
    
    .loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid #2c5aa0;
        border-top-color: transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        z-index: 1000;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
    
    img {
        transition: opacity 0.3s ease;
        opacity: 0;
    }
    
    img.loaded {
        opacity: 1;
    }
    
    .scroll-effect {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease;
    }
    
    .scroll-effect.active {
        opacity: 1;
        transform: translateY(0);
    }
`;

document.head.appendChild(style);
